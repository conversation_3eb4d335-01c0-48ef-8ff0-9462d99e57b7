"""
State management for the multi-agent system using latest LangGraph patterns.
Implements proper memory management and contextual state handling.
"""

from typing import Annotated, TypedDict, Literal, Any, Dict, List
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langgraph.graph.message import add_messages
from langgraph.graph import MessagesState


class ConversationContext(TypedDict):
    """Context information for maintaining conversation state"""
    user_intent: str  # Current user intent (search, booking, etc.)
    conversation_history: List[str]  # Key conversation points
    current_topic: str  # Current topic being discussed
    mentioned_entities: Dict[str, Any]  # Entities mentioned (apps, courses, etc.)
    problem_context: bool  # Whether user has a problem/issue
    last_search_type: str  # Last search type used
    booking_context: Dict[str, Any]  # Booking-related context


class AgentState(MessagesState):
    """
    Enhanced state schema for multi-agent system with proper memory management.
    Extends MessagesState to include conversation context and agent coordination.
    """
    # Conversation context for memory management
    context: ConversationContext
    
    # Agent coordination
    current_agent: str  # Which agent is currently active
    next_agent: str  # Which agent should be called next
    
    # Tool results and intermediate data
    search_results: str  # Results from search operations
    booking_data: Dict[str, Any]  # Booking-related data
    
    # Memory management
    conversation_summary: str  # Summary of conversation so far
    
    # User information
    user_id: str  # User identifier for personalization


class SearchAgentState(TypedDict):
    """Private state for search agent with specialized context"""
    messages: Annotated[List[BaseMessage], add_messages]
    context: ConversationContext
    search_query: str  # Translated search query
    search_type: Literal["information", "products"]  # Type of search
    search_results: str  # Raw search results


class BookingAgentState(TypedDict):
    """Private state for booking agent with booking-specific context"""
    messages: Annotated[List[BaseMessage], add_messages]
    context: ConversationContext
    booking_action: str  # Current booking action
    customer_info: Dict[str, str]  # Customer details
    selected_service: str  # Selected service/course
    available_slots: List[Dict[str, str]]  # Available time slots


def create_initial_context() -> ConversationContext:
    """Create initial conversation context"""
    return ConversationContext(
        user_intent="",
        conversation_history=[],
        current_topic="",
        mentioned_entities={},
        problem_context=False,
        last_search_type="",
        booking_context={}
    )


def create_initial_state(user_id: str = "default") -> AgentState:
    """Create initial agent state"""
    return AgentState(
        messages=[],
        context=create_initial_context(),
        current_agent="main",
        next_agent="",
        search_results="",
        booking_data={},
        conversation_summary="",
        user_id=user_id
    )


def update_context_with_message(context: ConversationContext, message: str) -> ConversationContext:
    """Update conversation context based on new message"""
    # Add to conversation history
    context["conversation_history"].append(message.lower())
    
    # Keep only last 10 messages for context
    if len(context["conversation_history"]) > 10:
        context["conversation_history"] = context["conversation_history"][-10:]
    
    # Detect problem context
    problem_keywords = ["chalena", "not working", "problem", "issue", "error", "dikka"]
    if any(keyword in message.lower() for keyword in problem_keywords):
        context["problem_context"] = True
    
    # Extract mentioned entities
    if "ambition guru" in message.lower() or "ambition" in message.lower():
        context["mentioned_entities"]["app"] = "Ambition Guru"
    
    if "ielts" in message.lower():
        context["mentioned_entities"]["course"] = "IELTS"
    
    if "nimabi" in message.lower():
        context["mentioned_entities"]["course"] = "NIMABI"
    
    # Detect user intent
    if any(word in message.lower() for word in ["book", "booking", "join", "class"]):
        context["user_intent"] = "booking"
    elif any(word in message.lower() for word in ["course", "kun kun", "what courses"]):
        context["user_intent"] = "course_inquiry"
    elif context["problem_context"]:
        context["user_intent"] = "troubleshooting"
    
    return context
