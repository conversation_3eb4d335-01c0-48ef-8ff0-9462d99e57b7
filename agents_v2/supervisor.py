"""
Main Supervisor Agent using latest LangGraph patterns.
Implements proper handoffs, memory management, and contextual decision making.
"""

import logging
from typing import Literal
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.types import Command
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import InMemorySaver

from .state import AgentState, update_context_with_message
from utils import log_user_input, log_agent_response, Colors

# Setup logging
logger = logging.getLogger(__name__)

# Setup LLM
supervisor_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1
)


@tool
def handoff_to_search_agent(user_message: str, search_type: Literal["information", "products"] = "information") -> Command:
    """
    Hand off to the search agent for information or product searches.
    
    Args:
        user_message: The user's exact message
        search_type: Type of search - "information" for troubleshooting, "products" for courses
    
    Returns:
        Command to navigate to search agent
    """
    logger.info(f"🔧 HANDOFF: search_agent(message='{user_message}', type='{search_type}')")
    
    return Command(
        goto="search_agent",
        update={
            "next_agent": "search_agent",
            "current_agent": "search_agent"
        }
    )


@tool  
def handoff_to_booking_agent(action: str = "get_slots", **kwargs) -> Command:
    """
    Hand off to the booking agent for appointment-related tasks.
    
    Args:
        action: The booking action - "get_slots", "book_appointment", "get_date"
        **kwargs: Additional parameters for the booking action
    
    Returns:
        Command to navigate to booking agent
    """
    logger.info(f"🔧 HANDOFF: booking_agent(action='{action}', kwargs={kwargs})")
    
    return Command(
        goto="booking_agent",
        update={
            "next_agent": "booking_agent", 
            "current_agent": "booking_agent",
            "booking_data": kwargs
        }
    )


def supervisor_node(state: AgentState) -> Command[Literal["search_agent", "booking_agent", END]]:
    """
    Main supervisor node that coordinates between sub-agents.
    Uses contextual information to make intelligent routing decisions.
    """
    messages = state["messages"]
    context = state["context"]
    
    if not messages:
        return Command(goto=END)
    
    # Get the latest user message
    latest_message = messages[-1]
    if not isinstance(latest_message, HumanMessage):
        return Command(goto=END)
    
    user_message = latest_message.content
    log_user_input(user_message)
    
    # Update conversation context
    updated_context = update_context_with_message(context, user_message)
    
    # Contextual decision making based on conversation history and current message
    conversation_text = " ".join(updated_context["conversation_history"])
    
    # Check for simple greetings
    simple_greetings = ["hello", "hi", "namaste", "hey"]
    if user_message.lower().strip() in simple_greetings and not updated_context["problem_context"]:
        response = AIMessage(content="Namaste! How can I help you today? I can assist you with course information, troubleshooting, or booking appointments.")
        log_agent_response(response.content)
        return Command(
            goto=END,
            update={
                "messages": [response],
                "context": updated_context,
                "current_agent": "supervisor"
            }
        )
    
    # Determine routing based on context and intent
    routing_decision = _determine_routing(user_message, updated_context, conversation_text)
    
    logger.info(f"🎯 ROUTING DECISION: {routing_decision}")
    
    if routing_decision["agent"] == "search_agent":
        return Command(
            goto="search_agent",
            update={
                "context": updated_context,
                "current_agent": "search_agent",
                "next_agent": "search_agent"
            }
        )
    elif routing_decision["agent"] == "booking_agent":
        return Command(
            goto="booking_agent", 
            update={
                "context": updated_context,
                "current_agent": "booking_agent",
                "next_agent": "booking_agent",
                "booking_data": routing_decision.get("booking_data", {})
            }
        )
    else:
        # Default response for unclear requests
        response = AIMessage(content="I'd be happy to help! Could you please clarify what you're looking for? I can help with course information, troubleshooting, or booking appointments.")
        log_agent_response(response.content)
        return Command(
            goto=END,
            update={
                "messages": [response],
                "context": updated_context,
                "current_agent": "supervisor"
            }
        )


def _determine_routing(user_message: str, context: dict, conversation_text: str) -> dict:
    """
    Determine which agent to route to based on contextual analysis.
    
    Args:
        user_message: Current user message
        context: Conversation context
        conversation_text: Full conversation history
        
    Returns:
        Dictionary with routing decision
    """
    user_lower = user_message.lower()
    
    # Check for booking intent
    booking_keywords = ["book", "booking", "appointment", "schedule", "join", "class", "enroll"]
    if any(keyword in user_lower for keyword in booking_keywords):
        return {
            "agent": "booking_agent",
            "reason": "booking_intent_detected",
            "booking_data": {"action": "get_slots"}
        }
    
    # Check for course/product inquiries
    course_keywords = ["course", "courses", "kun kun", "what", "available", "offer", "program", "class"]
    if any(keyword in user_lower for keyword in course_keywords) and not context["problem_context"]:
        return {
            "agent": "search_agent", 
            "search_type": "products",
            "reason": "course_inquiry_detected"
        }
    
    # Check for troubleshooting/problem context
    problem_keywords = ["chalena", "not working", "problem", "issue", "error", "dikka", "help"]
    if any(keyword in user_lower for keyword in problem_keywords) or context["problem_context"]:
        return {
            "agent": "search_agent",
            "search_type": "information", 
            "reason": "troubleshooting_detected"
        }
    
    # Context-aware routing based on conversation history
    if context["problem_context"] and any(app in user_lower for app in ["ambition", "app"]):
        return {
            "agent": "search_agent",
            "search_type": "information",
            "reason": "app_problem_context"
        }
    
    # Default to product search for general inquiries
    return {
        "agent": "search_agent",
        "search_type": "products", 
        "reason": "default_product_search"
    }


# Create supervisor tools
supervisor_tools = [handoff_to_search_agent, handoff_to_booking_agent]
