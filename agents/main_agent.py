"""
Main Agent Controller - Orchestrates sub-agents and tools
This is the primary agent that handles user interactions and delegates to specialized sub-agents.
"""

import os
import logging
from typing import Literal
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from utils import log_user_input, log_agent_response, log_tool_call, log_tool_result

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Define handoff tools for sub-agents
@tool
def handoff_to_search_agent(query: str, search_type: Literal["information", "products"] = "information") -> str:
    """
    Hand off to the search agent for information or product searches.

    Args:
        query: The search query
        search_type: Type of search - "information" for general info/troubleshooting,
                    "products" for courses/products

    Returns:
        Search results from the specialized search agent
    """
    log_tool_call("handoff_to_search_agent", f"query='{query}', search_type='{search_type}'")

    try:
        from .search_agent import SearchAgent

        search_agent = SearchAgent()
        if search_type == "products":
            logger.info(f"🎓 Using PRODUCTS search for: {query}")
            result = search_agent.search_products(query)
        else:
            logger.info(f"📋 Using INFORMATION search for: {query}")
            result = search_agent.search_information(query)

        log_tool_result(result)
        return result

    except Exception as e:
        error_msg = f"Error in search handoff: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def handoff_to_booking_agent(action: str, **kwargs) -> str:
    """
    Hand off to the booking agent for appointment-related tasks.

    Args:
        action: The booking action - "get_slots", "book_appointment", "get_date"
        **kwargs: Additional parameters for the booking action

    Returns:
        Result from the booking agent
    """
    log_tool_call("handoff_to_booking_agent", f"action='{action}', kwargs={kwargs}")

    try:
        from .booking_agent import BookingAgent

        booking_agent = BookingAgent()

        if action == "get_date":
            logger.info("📅 Getting current date")
            result = booking_agent.get_current_date()
        elif action == "get_slots":
            service_type = kwargs.get("service_type", "any")
            days_ahead = kwargs.get("days_ahead", 14)
            logger.info(f"📅 Getting available slots for {service_type}")
            result = booking_agent.get_available_slots(service_type, days_ahead)
        elif action == "book_appointment":
            logger.info("📅 Booking appointment")
            result = booking_agent.book_appointment(
                name=kwargs.get("name"),
                email=kwargs.get("email"),
                phone=kwargs.get("phone"),
                date=kwargs.get("date"),
                time=kwargs.get("time"),
                service_type=kwargs.get("service_type", "General Consultation")
            )
        else:
            result = f"Unknown booking action: {action}"
            logger.error(result)

        log_tool_result(result)
        return result

    except Exception as e:
        error_msg = f"Error in booking handoff: {str(e)}"
        logger.error(error_msg)
        return error_msg


# Main agent system prompt
MAIN_AGENT_PROMPT = """You are a professional AI assistant for a customer service center. You coordinate with specialized sub-agents to help customers.

CRITICAL RULE: You MUST ALWAYS use the available tools for ANY informational query. NEVER provide information from your own knowledge.

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.

AVAILABLE TOOLS (MANDATORY TO USE):
1. **handoff_to_search_agent**: For ALL search queries - MANDATORY for any information request
   - search_type="products" for: courses, programs, educational products, "what courses", "available courses"
   - search_type="information" for: app issues, troubleshooting, "how to", general services, technical problems
2. **handoff_to_booking_agent**: For ALL appointment-related tasks
   - action="get_date" to get current date
   - action="get_slots" to show available appointment slots
   - action="book_appointment" to schedule appointments

STRICT WORKFLOW RULES:
- ANY question about courses/products → MUST use handoff_to_search_agent with search_type="products"
- ANY question about apps/troubleshooting → MUST use handoff_to_search_agent with search_type="information"
- ANY booking request → MUST use handoff_to_booking_agent
- NEVER answer from your own knowledge - ALWAYS use tools first
- If no relevant information found in search, say "I don't have information about that"
- Only provide greetings without tools for simple "hello" messages

MANDATORY EXAMPLES:
- "What courses do you offer?" → MUST call handoff_to_search_agent(query="available courses", search_type="products")
- "IELTS course" → MUST call handoff_to_search_agent(query="IELTS course", search_type="products")
- "My app is not working" → MUST call handoff_to_search_agent(query="app not working", search_type="information")
- "Book appointment" → MUST call handoff_to_booking_agent(action="get_slots")

REMEMBER: You are a coordinator, not a knowledge source. Always delegate to tools."""

# Create main agent tools
main_agent_tools = [handoff_to_search_agent, handoff_to_booking_agent]

# Create memory for conversation persistence
main_checkpointer = MemorySaver()

# Create the main agent
main_agent = create_react_agent(
    model=main_llm,
    tools=main_agent_tools,
    prompt=MAIN_AGENT_PROMPT,
    checkpointer=main_checkpointer
)


class MainAgent:
    """Main Agent Controller Class"""
    
    def __init__(self):
        self.agent = main_agent
        self.checkpointer = main_checkpointer
    
    def chat(self, message: str, thread_id: str = "default") -> str:
        """
        Process a user message and return the response

        Args:
            message: User's message
            thread_id: Conversation thread ID

        Returns:
            Agent's response
        """
        log_user_input(message)

        config = {"configurable": {"thread_id": thread_id}}
        user_message = {"messages": [HumanMessage(message)]}

        try:
            response = self.agent.invoke(user_message, config=config)

            # Log tool usage
            tool_calls_made = []
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_calls_made.append(tool_call['name'])

            if tool_calls_made:
                logger.info(f"🔧 TOOLS USED: {tool_calls_made}")
            else:
                logger.warning("⚠️  NO TOOLS USED - Possible hallucination!")

            final_response = response["messages"][-1].content
            log_agent_response(final_response)

            return final_response
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_conversation_history(self, thread_id: str = "default") -> list:
        """Get conversation history for a thread"""
        config = {"configurable": {"thread_id": thread_id}}
        try:
            # This would need to be implemented based on the checkpointer's API
            return []
        except Exception:
            return []
