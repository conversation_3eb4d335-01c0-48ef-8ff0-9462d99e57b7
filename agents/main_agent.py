"""
Main Agent Controller - Orchestrates sub-agents and tools
This is the primary agent that handles user interactions and delegates to specialized sub-agents.
"""

import os
from typing import Literal
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver

# Load environment variables
load_dotenv()

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Define handoff tools for sub-agents
@tool
def handoff_to_search_agent(query: str, search_type: Literal["information", "products"] = "information") -> str:
    """
    Hand off to the search agent for information or product searches.
    
    Args:
        query: The search query
        search_type: Type of search - "information" for general info/troubleshooting, 
                    "products" for courses/products
    
    Returns:
        Search results from the specialized search agent
    """
    from .search_agent import SearchAgent
    
    search_agent = SearchAgent()
    if search_type == "products":
        return search_agent.search_products(query)
    else:
        return search_agent.search_information(query)


@tool
def handoff_to_booking_agent(action: str, **kwargs) -> str:
    """
    Hand off to the booking agent for appointment-related tasks.
    
    Args:
        action: The booking action - "get_slots", "book_appointment", "get_date"
        **kwargs: Additional parameters for the booking action
    
    Returns:
        Result from the booking agent
    """
    from .booking_agent import BookingAgent
    
    booking_agent = BookingAgent()
    
    if action == "get_date":
        return booking_agent.get_current_date()
    elif action == "get_slots":
        service_type = kwargs.get("service_type", "any")
        days_ahead = kwargs.get("days_ahead", 14)
        return booking_agent.get_available_slots(service_type, days_ahead)
    elif action == "book_appointment":
        return booking_agent.book_appointment(
            name=kwargs.get("name"),
            email=kwargs.get("email"),
            phone=kwargs.get("phone"),
            date=kwargs.get("date"),
            time=kwargs.get("time"),
            service_type=kwargs.get("service_type", "General Consultation")
        )
    else:
        return f"Unknown booking action: {action}"


# Main agent system prompt
MAIN_AGENT_PROMPT = """You are a professional AI assistant for a customer service center. You coordinate with specialized sub-agents to help customers.

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both.

AVAILABLE SUB-AGENTS:
1. **Search Agent** (via handoff_to_search_agent): Handles all search queries
   - Use search_type="information" for: app issues, troubleshooting, "how to", general services
   - Use search_type="products" for: course information, course booking, available programs
2. **Booking Agent** (via handoff_to_booking_agent): Handles all appointment-related tasks
   - Use action="get_date" to get current date
   - Use action="get_slots" to show available appointment slots
   - Use action="book_appointment" to schedule appointments

WORKFLOW RULES:
- For ANY search query, use handoff_to_search_agent with appropriate search_type
- For booking workflows: 
  1. Search for course/service info (if needed) using search agent
  2. Get current date using booking agent
  3. Show available slots using booking agent
  4. Collect customer details
  5. Book appointment using booking agent
- NEVER answer informational questions without using the search agent
- Always delegate to the appropriate sub-agent rather than trying to answer directly
- Provide clean, helpful responses without showing the handoff process

EXAMPLES:
- "What courses do you offer?" → handoff_to_search_agent(query="available courses", search_type="products")
- "My app is not working" → handoff_to_search_agent(query="app not working troubleshooting", search_type="information")
- "Book IELTS course" → Search for IELTS info, then booking workflow
- "Any available slots?" → handoff_to_booking_agent(action="get_slots")

Remember: Always use sub-agents for their specialized tasks. Your role is to coordinate and provide a smooth user experience."""

# Create main agent tools
main_agent_tools = [handoff_to_search_agent, handoff_to_booking_agent]

# Create memory for conversation persistence
main_checkpointer = MemorySaver()

# Create the main agent
main_agent = create_react_agent(
    model=main_llm,
    tools=main_agent_tools,
    prompt=MAIN_AGENT_PROMPT,
    checkpointer=main_checkpointer
)


class MainAgent:
    """Main Agent Controller Class"""
    
    def __init__(self):
        self.agent = main_agent
        self.checkpointer = main_checkpointer
    
    def chat(self, message: str, thread_id: str = "default") -> str:
        """
        Process a user message and return the response
        
        Args:
            message: User's message
            thread_id: Conversation thread ID
            
        Returns:
            Agent's response
        """
        config = {"configurable": {"thread_id": thread_id}}
        user_message = {"messages": [HumanMessage(message)]}
        
        try:
            response = self.agent.invoke(user_message, config=config)
            return response["messages"][-1].content
        except Exception as e:
            return f"Error processing request: {str(e)}"
    
    def get_conversation_history(self, thread_id: str = "default") -> list:
        """Get conversation history for a thread"""
        config = {"configurable": {"thread_id": thread_id}}
        try:
            # This would need to be implemented based on the checkpointer's API
            return []
        except Exception:
            return []
