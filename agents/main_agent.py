"""
Main Agent Controller - Orchestrates sub-agents and tools
This is the primary agent that handles user interactions and delegates to specialized sub-agents.
"""

import os
import logging
from typing import Literal
from dotenv import load_dotenv
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.tools import tool
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.memory import MemorySaver
from utils import log_user_input, log_agent_response, log_tool_call, log_tool_result

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Global search agent instance to maintain conversation context
_global_search_agent = None

def get_search_agent():
    """Get the global search agent instance"""
    global _global_search_agent
    if _global_search_agent is None:
        from .search_agent import SearchAgent
        _global_search_agent = SearchAgent()
    return _global_search_agent

# Define handoff tools for sub-agents
@tool
def handoff_to_search_agent(user_message: str, search_type: Literal["information", "products"] = "information") -> str:
    """
    Hand off to the search agent for information or product searches.

    Args:
        user_message: The user's exact message (will be translated by search agent)
        search_type: Type of search - "information" for general info/troubleshooting,
                    "products" for courses/products

    Returns:
        Search results from the specialized search agent
    """
    log_tool_call("handoff_to_search_agent", f"user_message='{user_message}', search_type='{search_type}'")

    try:
        # Use global search agent to maintain conversation context
        search_agent = get_search_agent()

        if search_type == "products":
            logger.info(f"🎓 Using PRODUCTS search for: {user_message}")
            result = search_agent.search_products(user_message)
        else:
            logger.info(f"📋 Using INFORMATION search for: {user_message}")
            result = search_agent.search_information(user_message)

        log_tool_result(result)
        return result

    except Exception as e:
        error_msg = f"Error in search handoff: {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool
def handoff_to_booking_agent(action: str, **kwargs) -> str:
    """
    Hand off to the booking agent for appointment-related tasks.

    Args:
        action: The booking action - "get_slots", "book_appointment", "get_date"
        **kwargs: Additional parameters for the booking action

    Returns:
        Result from the booking agent
    """
    log_tool_call("handoff_to_booking_agent", f"action='{action}', kwargs={kwargs}")

    try:
        from .booking_agent import BookingAgent

        booking_agent = BookingAgent()

        if action == "get_date":
            logger.info("📅 Getting current date")
            result = booking_agent.get_current_date()
        elif action == "get_slots":
            service_type = kwargs.get("service_type", "any")
            days_ahead = kwargs.get("days_ahead", 14)
            logger.info(f"📅 Getting available slots for {service_type}")
            result = booking_agent.get_available_slots(service_type, days_ahead)
        elif action == "book_appointment":
            logger.info("📅 Booking appointment")
            result = booking_agent.book_appointment(
                name=kwargs.get("name"),
                email=kwargs.get("email"),
                phone=kwargs.get("phone"),
                date=kwargs.get("date"),
                time=kwargs.get("time"),
                service_type=kwargs.get("service_type", "General Consultation")
            )
        else:
            result = f"Unknown booking action: {action}"
            logger.error(result)

        log_tool_result(result)
        return result

    except Exception as e:
        error_msg = f"Error in booking handoff: {str(e)}"
        logger.error(error_msg)
        return error_msg


# Main agent system prompt
MAIN_AGENT_PROMPT = """You are a professional, friendly sales representative for a customer service center. You help customers find solutions and courses that meet their needs.

CRITICAL RULES:
1. ALWAYS use tools for ANY informational query - NEVER provide information from your own knowledge
2. Act as a helpful sales person - be engaging, solution-oriented, and customer-focused
3. Use search results to provide refined, helpful responses (not raw search output)
4. Remember conversation context and build on previous interactions

The message can be in Nepali (नेपाली), Romanized Nepali, English, or a mix of both. Respond in the same language style as the user.

AVAILABLE TOOLS (MANDATORY TO USE):
1. **handoff_to_search_agent**: For ALL search queries - pass the user's EXACT message
   - search_type="products" for: courses, programs, educational products, "what courses", "available courses"
   - search_type="information" for: app issues, troubleshooting, "how to", general services, technical problems
2. **handoff_to_booking_agent**: For ALL appointment-related tasks
   - action="get_date" to get current date
   - action="get_slots" to show available appointment slots
   - action="book_appointment" to schedule appointments

STRICT WORKFLOW RULES:
- ANY question about courses/products → MUST use handoff_to_search_agent with search_type="products"
- ANY question about apps/troubleshooting/problems → MUST use handoff_to_search_agent with search_type="information"
- ANY booking request → MUST use handoff_to_booking_agent
- NEVER answer from your own knowledge - ALWAYS use tools first
- If user mentions problems like "chalena", "not working", "issue" → MUST use search_type="information"
- Only simple greetings like "hello", "hi", "namaste" don't need tools
- For ALL other queries, use appropriate search tool first

RESPONSE STYLE:
- Be a helpful sales person, not a robot
- Use search results to craft engaging, solution-focused responses
- Offer additional help and related services
- Show empathy for problems and enthusiasm for solutions
- Ask follow-up questions to better understand customer needs
- Suggest relevant courses or services based on search results

WORKFLOW:
1. Use tools to get information (pass user's exact message to search agent)
2. Analyze the tool results
3. Craft a helpful, sales-oriented response using the information
4. Offer additional assistance or related services
5. For problems, show empathy and provide solutions
6. For course inquiries, highlight benefits and encourage enrollment

EXAMPLES:
- User: "Mero app chalena" → Use search tool → Respond with empathy + solutions + offer help
- User: "What courses?" → Use search tool → Present courses enthusiastically + highlight benefits
- User: "IELTS course" → Use search tool → Explain IELTS course + benefits + encourage enrollment

REMEMBER: You're a sales person using tools to help customers. Be helpful, engaging, and solution-focused!"""

# Create main agent tools
main_agent_tools = [handoff_to_search_agent, handoff_to_booking_agent]

# Create memory for conversation persistence
main_checkpointer = MemorySaver()

# Create the main agent
main_agent = create_react_agent(
    model=main_llm,
    tools=main_agent_tools,
    prompt=MAIN_AGENT_PROMPT,
    checkpointer=main_checkpointer
)


class MainAgent:
    """Main Agent Controller Class"""
    
    def __init__(self):
        self.agent = main_agent
        self.checkpointer = main_checkpointer
    
    def chat(self, message: str, thread_id: str = "default") -> str:
        """
        Process a user message and return the response

        Args:
            message: User's message
            thread_id: Conversation thread ID

        Returns:
            Agent's response
        """
        log_user_input(message)

        config = {"configurable": {"thread_id": thread_id}}
        user_message = {"messages": [HumanMessage(message)]}

        try:
            response = self.agent.invoke(user_message, config=config)

            # Log tool usage
            tool_calls_made = []
            for msg in response["messages"]:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_calls_made.append(tool_call['name'])

            if tool_calls_made:
                logger.info(f"🔧 TOOLS USED: {tool_calls_made}")
            else:
                logger.warning("⚠️  NO TOOLS USED - Possible hallucination!")

            final_response = response["messages"][-1].content
            log_agent_response(final_response)

            return final_response
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_conversation_history(self, thread_id: str = "default") -> list:
        """Get conversation history for a thread"""
        config = {"configurable": {"thread_id": thread_id}}
        try:
            # This would need to be implemented based on the checkpointer's API
            return []
        except Exception:
            return []
