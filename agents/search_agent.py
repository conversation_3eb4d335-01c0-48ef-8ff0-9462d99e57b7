"""
Search Sub-Agent - Specialized for information and product searches
Handles two types of searches:
1. Information Search - general info, troubleshooting, apps, services
2. Product Search - courses, educational products, booking-related queries
"""

import logging
from config import get_vector_store_manager

# Setup logging
logger = logging.getLogger(__name__)


class SearchAgent:
    """Search Agent for handling information and product searches"""

    def __init__(self):
        # Use the global vector store manager (initialized only once)
        self.vector_manager = get_vector_store_manager()
        # Store conversation context for better query understanding
        self.conversation_context = []
        logger.info("✅ Search Agent initialized with global vector store manager")

    def _translate_query(self, user_message: str, search_type: str) -> str:
        """
        Translate user message to a proper search query using context

        Args:
            user_message: Raw user input
            search_type: Type of search (information/products)

        Returns:
            Translated search query
        """
        # Add current message to context
        self.conversation_context.append(user_message.lower())

        # Keep only last 5 messages for context
        if len(self.conversation_context) > 5:
            self.conversation_context = self.conversation_context[-5:]

        # Context-aware query translation
        context_text = " ".join(self.conversation_context)

        # Common Nepali/Romanized patterns
        if "mero app" in user_message.lower() or "app chalena" in user_message.lower():
            if "ambition guru" in context_text or "ambition" in context_text:
                return "Ambition Guru app troubleshooting problems not working"
            else:
                return "app not working troubleshooting mobile application problems"

        if "app bhaneko k" in user_message.lower() or "app ke ho" in user_message.lower():
            if "ambition guru" in context_text or "ambition" in context_text:
                return "Ambition Guru app features what is description"
            else:
                return "what is app application features"

        if search_type == "products":
            # Product-specific translations
            if "course" in user_message.lower() or "kors" in user_message.lower():
                return f"available courses programs {user_message}"
            elif "ielts" in user_message.lower():
                return "IELTS English proficiency test preparation course"
            elif "loksewa" in user_message.lower():
                return "Loksewa government exam preparation course"
            else:
                return f"courses programs education {user_message}"
        else:
            # Information-specific translations
            if "chalena" in user_message.lower() or "not working" in user_message.lower():
                return f"troubleshooting problems not working {user_message}"
            elif "kasari" in user_message.lower() or "how to" in user_message.lower():
                return f"how to guide instructions {user_message}"
            else:
                return f"information help guide {user_message}"

    def search_information(self, user_message: str) -> str:
        """
        Search for general information, troubleshooting, apps, and services.

        Args:
            user_message: The user's original message

        Returns:
            A formatted string with search results
        """
        # Translate user message to proper search query
        search_query = self._translate_query(user_message, "information")
        logger.info(f"📋 Translated query: '{user_message}' → '{search_query}'")

        # Search using translated query
        return self.vector_manager.search_information(search_query)

    def search_products(self, user_message: str) -> str:
        """
        Search for products, courses, and educational programs.

        Args:
            user_message: The user's original message

        Returns:
            A formatted string with product/course search results
        """
        # Translate user message to proper search query
        search_query = self._translate_query(user_message, "products")
        logger.info(f"🎓 Translated query: '{user_message}' → '{search_query}'")

        # Search using translated query
        return self.vector_manager.search_products(search_query)
