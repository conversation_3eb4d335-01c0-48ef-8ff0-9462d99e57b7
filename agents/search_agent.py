"""
Search Sub-Agent - Specialized for information and product searches
Handles two types of searches:
1. Information Search - general info, troubleshooting, apps, services
2. Product Search - courses, educational products, booking-related queries
"""

import logging
from config import get_vector_store_manager

# Setup logging
logger = logging.getLogger(__name__)


class SearchAgent:
    """Search Agent for handling information and product searches"""

    def __init__(self):
        # Use the global vector store manager (initialized only once)
        self.vector_manager = get_vector_store_manager()
        # Store conversation context for better query understanding
        self.conversation_context = []
        logger.info("✅ Search Agent initialized with global vector store manager")

    def _translate_query(self, user_message: str, search_type: str) -> tuple[str, str]:
        """
        Translate user message to a proper search query using context

        Args:
            user_message: Raw user input
            search_type: Type of search (information/products)

        Returns:
            Tuple of (translated_query, corrected_search_type)
        """
        # Add current message to context
        self.conversation_context.append(user_message.lower())

        # Keep only last 5 messages for context
        if len(self.conversation_context) > 5:
            self.conversation_context = self.conversation_context[-5:]

        # Context-aware query translation
        context_text = " ".join(self.conversation_context)

        # Check if previous messages indicate a problem/troubleshooting context
        problem_context = any(word in context_text for word in [
            "chalena", "not working", "problem", "issue", "error", "mero app"
        ])

        # Smart context-aware translation
        user_lower = user_message.lower()

        # If user mentions an app name after expressing a problem, it's troubleshooting
        if problem_context and ("ambition guru" in user_lower or "ambition" in user_lower):
            return "Ambition Guru app troubleshooting problems not working", "information"

        # Direct problem statements
        if "mero app" in user_lower or "app chalena" in user_lower:
            if "ambition guru" in context_text or "ambition" in context_text:
                return "Ambition Guru app troubleshooting problems not working", "information"
            else:
                return "app not working troubleshooting mobile application problems", "information"

        # App information requests
        if "app bhaneko k" in user_lower or "app ke ho" in user_lower:
            if "ambition guru" in context_text or "ambition" in context_text:
                return "Ambition Guru app features what is description", "information"
            else:
                return "what is app application features", "information"

        # If it's a specific app name and we have problem context, treat as troubleshooting
        if problem_context and any(app in user_lower for app in ["ambition guru", "ambition"]):
            return f"{user_message} troubleshooting problems not working", "information"

        # Course-related queries (only if no problem context)
        if not problem_context and search_type == "products":
            if "course" in user_lower or "kors" in user_lower:
                return f"available courses programs {user_message}", "products"
            elif "ielts" in user_lower:
                return "IELTS English proficiency test preparation course", "products"
            elif "loksewa" in user_lower:
                return "Loksewa government exam preparation course", "products"
            else:
                return f"courses programs education {user_message}", "products"

        # Information/troubleshooting queries
        if search_type == "information" or problem_context:
            if "chalena" in user_lower or "not working" in user_lower:
                return f"troubleshooting problems not working {user_message}", "information"
            elif "kasari" in user_lower or "how to" in user_lower:
                return f"how to guide instructions {user_message}", "information"
            else:
                return f"information help guide {user_message}", "information"

        # Default fallback
        return f"{search_type} {user_message}", search_type

    def search_information(self, user_message: str) -> str:
        """
        Search for general information, troubleshooting, apps, and services.

        Args:
            user_message: The user's original message

        Returns:
            A formatted string with search results
        """
        # Translate user message to proper search query with context awareness
        search_query, corrected_type = self._translate_query(user_message, "information")
        logger.info(f"📋 Translated query: '{user_message}' → '{search_query}' (type: {corrected_type})")

        # Use the corrected search type
        if corrected_type == "products":
            return self.vector_manager.search_products(search_query)
        else:
            return self.vector_manager.search_information(search_query)

    def search_products(self, user_message: str) -> str:
        """
        Search for products, courses, and educational programs.

        Args:
            user_message: The user's original message

        Returns:
            A formatted string with product/course search results
        """
        # Translate user message to proper search query with context awareness
        search_query, corrected_type = self._translate_query(user_message, "products")
        logger.info(f"🎓 Translated query: '{user_message}' → '{search_query}' (type: {corrected_type})")

        # Use the corrected search type
        if corrected_type == "information":
            return self.vector_manager.search_information(search_query)
        else:
            return self.vector_manager.search_products(search_query)
