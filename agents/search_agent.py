"""
Search Sub-Agent - Specialized for information and product searches
Handles two types of searches:
1. Information Search - general info, troubleshooting, apps, services
2. Product Search - courses, educational products, booking-related queries
"""

import logging
from config import get_vector_store_manager

# Setup logging
logger = logging.getLogger(__name__)


class SearchAgent:
    """Search Agent for handling information and product searches"""

    def __init__(self):
        # Use the global vector store manager (initialized only once)
        self.vector_manager = get_vector_store_manager()
        logger.info("✅ Search Agent initialized with global vector store manager")

    def search_information(self, query: str) -> str:
        """
        Search for general information, troubleshooting, apps, and services.

        Args:
            query: The search term to look for

        Returns:
            A formatted string with search results
        """
        # Delegate to the global vector store manager
        return self.vector_manager.search_information(query)

    def search_products(self, query: str) -> str:
        """
        Search for products, courses, and educational programs.

        Args:
            query: The search term to look for (course name, subject, etc.)

        Returns:
            A formatted string with product/course search results
        """
        # Delegate to the global vector store manager
        return self.vector_manager.search_products(query)
