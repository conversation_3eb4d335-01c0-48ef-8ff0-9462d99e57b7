"""
Colored logging utility for better visibility of agent interactions.
"""

import logging
from typing import Any


class Colors:
    """ANSI color codes for terminal output"""
    # Basic colors
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    
    # Bright colors
    BRIGHT_RED = '\033[91;1m'
    BRIGHT_GREEN = '\033[92;1m'
    BRIGHT_YELLOW = '\033[93;1m'
    BRIGHT_BLUE = '\033[94;1m'
    BRIGHT_MAGENTA = '\033[95;1m'
    BRIGHT_CYAN = '\033[96;1m'
    
    # Background colors
    BG_RED = '\033[101m'
    BG_GREEN = '\033[102m'
    BG_YELLOW = '\033[103m'
    BG_BLUE = '\033[104m'
    
    # Styles
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    RESET = '\033[0m'


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for different log levels and components"""
    
    def __init__(self):
        super().__init__()
        
        # Define color schemes for different components
        self.colors = {
            'user_input': Colors.BRIGHT_CYAN + Colors.BOLD,
            'main_agent': Colors.BRIGHT_GREEN + Colors.BOLD,
            'tool_call': Colors.BRIGHT_YELLOW + Colors.BOLD,
            'search_info': Colors.BLUE,
            'search_products': Colors.MAGENTA,
            'booking': Colors.CYAN,
            'error': Colors.BRIGHT_RED + Colors.BOLD,
            'warning': Colors.YELLOW,
            'success': Colors.GREEN,
            'vector_store': Colors.WHITE,
        }
    
    def format(self, record):
        # Get the original message
        message = record.getMessage()
        
        # Apply colors based on message content
        if "👤 USER INPUT:" in message:
            colored_message = f"{self.colors['user_input']}👤 USER: {message.split('👤 USER INPUT: ')[1]}{Colors.RESET}"
        elif "🤖 AGENT RESPONSE:" in message:
            response = message.split('🤖 AGENT RESPONSE: ')[1]
            colored_message = f"{self.colors['main_agent']}🤖 MAIN AGENT: {response}{Colors.RESET}"
        elif "🔧 TOOL CALLED:" in message:
            tool_info = message.split('🔧 TOOL CALLED: ')[1]
            colored_message = f"{self.colors['tool_call']}🔧 TOOL CALLED: {tool_info}{Colors.RESET}"
        elif "🔧 TOOLS USED:" in message:
            tools = message.split('🔧 TOOLS USED: ')[1]
            colored_message = f"{self.colors['tool_call']}🔧 TOOLS USED: {tools}{Colors.RESET}"
        elif "📋 INFORMATION SEARCH:" in message:
            query = message.split('📋 INFORMATION SEARCH: ')[1]
            colored_message = f"{self.colors['search_info']}📋 INFO SEARCH: {query}{Colors.RESET}"
        elif "🎓 PRODUCTS SEARCH:" in message:
            query = message.split('🎓 PRODUCTS SEARCH: ')[1]
            colored_message = f"{self.colors['search_products']}🎓 PRODUCT SEARCH: {query}{Colors.RESET}"
        elif "📋 Information search result:" in message:
            result = message.split('📋 Information search result: ')[1]
            colored_message = f"{self.colors['search_info']}📋 INFO RESULT: {result[:100]}...{Colors.RESET}"
        elif "🎓 Products search result:" in message:
            result = message.split('🎓 Products search result: ')[1]
            colored_message = f"{self.colors['search_products']}🎓 PRODUCT RESULT: {result[:100]}...{Colors.RESET}"
        elif "📅" in message:
            colored_message = f"{self.colors['booking']}{message}{Colors.RESET}"
        elif "✅ Search result:" in message:
            result = message.split('✅ Search result: ')[1]
            colored_message = f"{self.colors['success']}✅ TOOL RESULT: {result[:100]}...{Colors.RESET}"
        elif "⚠️" in message:
            colored_message = f"{self.colors['warning']}{message}{Colors.RESET}"
        elif "❌" in message or "Error" in message:
            colored_message = f"{self.colors['error']}{message}{Colors.RESET}"
        elif "🔧 Initializing" in message or "Vector Store" in message:
            colored_message = f"{self.colors['vector_store']}{message}{Colors.RESET}"
        elif "✅" in message:
            colored_message = f"{self.colors['success']}{message}{Colors.RESET}"
        else:
            colored_message = message
        
        return colored_message


def setup_colored_logging():
    """Setup colored logging for the entire application"""
    # Create a custom logger
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create console handler with colored formatter
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(ColoredFormatter())
    
    # Add handler to logger
    logger.addHandler(console_handler)
    
    return logger


def log_user_input(message: str):
    """Log user input with special formatting"""
    logger = logging.getLogger(__name__)
    logger.info(f"👤 USER INPUT: {message}")


def log_agent_response(response: str):
    """Log agent response with special formatting"""
    logger = logging.getLogger(__name__)
    logger.info(f"🤖 AGENT RESPONSE: {response}")


def log_tool_call(tool_name: str, args: Any):
    """Log tool call with special formatting"""
    logger = logging.getLogger(__name__)
    logger.info(f"🔧 TOOL CALLED: {tool_name}({args})")


def log_tool_result(result: str):
    """Log tool result with special formatting"""
    logger = logging.getLogger(__name__)
    logger.info(f"✅ Search result: {result}")


def log_separator():
    """Log a visual separator"""
    logger = logging.getLogger(__name__)
    print(f"{Colors.CYAN}{'='*80}{Colors.RESET}")


def log_section_header(title: str):
    """Log a section header"""
    logger = logging.getLogger(__name__)
    print(f"\n{Colors.BRIGHT_BLUE}{Colors.BOLD}🔹 {title} 🔹{Colors.RESET}")
