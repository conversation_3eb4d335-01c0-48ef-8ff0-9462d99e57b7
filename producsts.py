products = [
    {
        "name": "SEE Bridge Course",
        "code": "SEE-BRIDGE",
        "description": "Intensive course to prepare students transitioning from Grade 10 (SEE) to +2 level."
    },
    {
        "name": "BBS 1st Semester",
        "code": "BBS-SEM1",
        "description": "Bachelor of Business Studies first semester course under Tribhuvan University."
    },
    {
        "name": "BBA 1st Semester",
        "code": "BBA-SEM1",
        "description": "Bachelor of Business Administration first semester course for management students."
    },
    {
        "name": "CSIT Entrance Prep",
        "code": "CSIT-ENT",
        "description": "Preparation course for BSc CSIT entrance exam under Tribhuvan University."
    },
    {
        "name": "IELTS Preparation",
        "code": "IELTS-PREP",
        "description": "English proficiency test preparation course for international study or migration."
    },
    {
        "name": "Korean Language - TOPIK Level 1",
        "code": "KOR-L1",
        "description": "Beginner-level Korean language course targeting TOPIK Level 1 exam."
    },
    {
        "name": "Korean Language - TOPIK Level 2",
        "code": "KOR-L2",
        "description": "Intermediate Korean language course targeting TOPIK Level 2 for EPS Korea."
    },
    {
        "name": "German Language - A1 Level",
        "code": "GER-A1",
        "description": "Basic German language course aligned with CEFR A1 level for study/work abroad."
    },
    {
        "name": "German Language - B1 Level",
        "code": "GER-B1",
        "description": "Intermediate German language course (CEFR B1) for higher-level proficiency."
    },
    {
        "name": "EIS Grade 11",
        "code": "EIS-G11",
        "description": "Education in Science Grade 11 curriculum focused on core science subjects."
    },
    {
        "name": "WWIS Grade 12",
        "code": "WWIS-G12",
        "description": "World Wide Information Science Grade 12 content for IT and general knowledge."
    },
    {
        "name": "NASU Level 1 Preparation",
        "code": "NASU-L1",
        "description": "Preparation for NASU Level 1 government exam including general knowledge and aptitude."
    },
    {
        "name": "NIMABI Exam Prep",
        "code": "NIMABI-PREP",
        "description": "Training for the Nepal Madarsa Board (NIMABI) exams for Islamic education."
    },
]
