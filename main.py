"""
Multi-Agent Customer Service System
Main application entry point with clean interface and proper agent coordination.
"""

import uuid
from agents import MainAgent


def chat_interface():
    """Interactive chat interface for the multi-agent system"""
    print("=" * 60)
    print("🤖 MULTI-AGENT CUSTOMER SERVICE SYSTEM")
    print("=" * 60)
    print("Available services:")
    print("• Search for course/product information")
    print("• Search for general information and troubleshooting")
    print("• Book appointments for courses")
    print("• General customer support")
    print("\nType 'quit' to exit, 'new' to start a new conversation")
    print("=" * 60)
    
    # Initialize main agent
    main_agent = MainAgent()
    
    # Create a unique thread for this conversation
    thread_id = str(uuid.uuid4())
    
    while True:
        user_input = input("\n👤 You: ").strip()
        
        if user_input.lower() == 'quit':
            print("\n🤖 Thank you for using our service! Have a great day!")
            break
        elif user_input.lower() == 'new':
            thread_id = str(uuid.uuid4())
            print("\n🤖 Starting a new conversation...")
            continue
        elif not user_input:
            continue
        
        try:
            print("\n🤖 Assistant: ", end="", flush=True)
            response = main_agent.chat(user_input, thread_id)
            print(response)
            
        except Exception as e:
            print(f"\n❌ Error: {str(e)}")
            print("Please try again or contact support.")


def demo_conversation():
    """Run a demo conversation to show the multi-agent system capabilities"""
    print("\n" + "=" * 60)
    print("🎯 DEMO CONVERSATION")
    print("=" * 60)
    
    demo_inputs = [
        "Hello! What courses do you offer?",
        "I'm interested in IELTS preparation course",
        "My app is not working properly, can you help?",
        "I'd like to book the IELTS course",
        "My name is John Smith, <NAME_EMAIL>, phone is 555-0123",
        "I'd like to schedule for tomorrow at 2PM"
    ]
    
    main_agent = MainAgent()
    thread_id = str(uuid.uuid4())
    
    for user_input in demo_inputs:
        print(f"\n👤 User: {user_input}")
        
        try:
            response = main_agent.chat(user_input, thread_id)
            print(f"🤖 Assistant: {response}")
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")


def test_agents():
    """Test the individual agents and their capabilities"""
    print("\n🧪 Testing Multi-Agent System...")
    
    # Test main agent
    print("\n1. Testing Main Agent:")
    try:
        main_agent = MainAgent()
        response = main_agent.chat("Hello, what services do you offer?")
        print(f"✅ Main Agent works: {response}")
    except Exception as e:
        print(f"❌ Main Agent error: {e}")
        return
    
    # Test search agent directly
    print("\n2. Testing Search Agent:")
    try:
        from agents import SearchAgent
        search_agent = SearchAgent()
        
        info_result = search_agent.search_information("app troubleshooting")
        print(f"✅ Information search: {info_result[:100]}...")
        
        product_result = search_agent.search_products("IELTS course")
        print(f"✅ Product search: {product_result[:100]}...")
        
    except Exception as e:
        print(f"❌ Search Agent error: {e}")
    
    # Test booking agent directly
    print("\n3. Testing Booking Agent:")
    try:
        from agents import BookingAgent
        booking_agent = BookingAgent()
        
        date_result = booking_agent.get_current_date()
        print(f"✅ Date function: {date_result.split()[0:5]}")
        
        slots_result = booking_agent.get_available_slots()
        print(f"✅ Slots function: {slots_result[:100]}...")
        
    except Exception as e:
        print(f"❌ Booking Agent error: {e}")
    
    print("\n✅ Multi-agent system testing completed!")


if __name__ == "__main__":
    print("Choose an option:")
    print("1. Interactive Chat")
    print("2. Demo Conversation")
    print("3. Test Agents")
    
    choice = input("Enter your choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        chat_interface()
    elif choice == "2":
        demo_conversation()
    elif choice == "3":
        test_agents()
    else:
        print("Invalid choice. Starting interactive chat...")
        chat_interface()
