"""
Global Vector Store Configuration
Singleton pattern to ensure vector stores and embeddings are created only once.
"""

import os
import logging
from typing import Optional
from dotenv import load_dotenv
from qdrant_client import QdrantClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)


class VectorStoreManager:
    """Singleton class to manage vector stores and embeddings"""
    
    _instance: Optional['VectorStoreManager'] = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            logger.info("🔧 Initializing Vector Store Manager (one-time setup)")
            self._setup_connections()
            self._setup_chains()
            VectorStoreManager._initialized = True
            logger.info("✅ Vector Store Manager initialized successfully")
    
    def _setup_connections(self):
        """Setup Qdrant connections and vector stores - called only once"""
        logger.info("📡 Setting up Qdrant connections...")
        
        # Setup embeddings
        self.embeddings = OpenAIEmbeddings(
            model="text-embedding-3-large",
            api_key=os.getenv("OPENAI_API_KEY"),
            dimensions=1536
        )
        logger.info("✅ Embeddings model loaded")
        
        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(
            host="*************",
            port=6333,
        )
        logger.info("✅ Qdrant client connected")
        
        # Setup Information Vector Store (general info collection)
        self.info_vector_store = QdrantVectorStore(
            client=self.qdrant_client,
            collection_name="langsmit_test",
            embedding=self.embeddings,
            retrieval_mode=RetrievalMode.DENSE,
            content_payload_key="page_content",
            metadata_payload_key="metadata"
        )
        logger.info("✅ Information vector store connected (langsmit_test)")
        
        # Setup Products Vector Store (products collection)
        self.products_vector_store = QdrantVectorStore(
            client=self.qdrant_client,
            collection_name="langchain_products",
            embedding=self.embeddings,
            retrieval_mode=RetrievalMode.DENSE,
            content_payload_key="page_content",
            metadata_payload_key="metadata"
        )
        logger.info("✅ Products vector store connected (langchain_products)")
        
        # Create retrievers
        self.info_retriever = self.info_vector_store.as_retriever(
            search_type="mmr", 
            search_kwargs={"k": 5}
        )
        self.products_retriever = self.products_vector_store.as_retriever(
            search_type="mmr", 
            search_kwargs={"k": 5}
        )
        logger.info("✅ Retrievers created")
    
    def _setup_chains(self):
        """Setup LLM and retrieval chains - called only once"""
        logger.info("🔗 Setting up retrieval chains...")
        
        # Setup LLM
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            temperature=0.1,
            google_api_key=os.getenv("GOOGLE_API_KEY")
        )
        logger.info("✅ LLM initialized")
        
        # Create prompts for different search types
        info_system_prompt = (
            "You are an assistant for question-answering tasks about general information, "
            "troubleshooting, apps, and services. Use the following pieces of retrieved context "
            "to answer the question. If you don't know the answer, say that you don't know. "
            "Use three sentences maximum and keep the answer concise."
            "\n\n"
            "{context}"
        )
        
        products_system_prompt = (
            "You are an assistant for product and course information. Use the following pieces "
            "of retrieved context to answer questions about available courses, programs, "
            "and educational products. Include course codes, names, and descriptions when relevant. "
            "If you don't know the answer, say that you don't know. "
            "Use three sentences maximum and keep the answer concise."
            "\n\n"
            "{context}"
        )
        
        info_prompt = ChatPromptTemplate.from_messages([
            ("system", info_system_prompt),
            ("human", "{input}"),
        ])
        
        products_prompt = ChatPromptTemplate.from_messages([
            ("system", products_system_prompt),
            ("human", "{input}"),
        ])
        
        # Create document chains
        info_question_answer_chain = create_stuff_documents_chain(self.llm, info_prompt)
        products_question_answer_chain = create_stuff_documents_chain(self.llm, products_prompt)
        
        # Create retrieval chains
        self.info_qa_chain = create_retrieval_chain(self.info_retriever, info_question_answer_chain)
        self.products_qa_chain = create_retrieval_chain(self.products_retriever, products_question_answer_chain)
        logger.info("✅ Retrieval chains created")
    
    def search_information(self, query: str) -> str:
        """Search for general information using the global chain"""
        logger.info(f"📋 INFORMATION SEARCH: {query}")
        try:
            answer = self.info_qa_chain.invoke({"input": query})
            result = answer["answer"]
            logger.info(f"📋 Information search result: {result}")
            return f"📋 Information Search Results:\n{result}"
        except Exception as e:
            error_msg = f"Error searching information database: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def search_products(self, query: str) -> str:
        """Search for products using the global chain"""
        logger.info(f"🎓 PRODUCTS SEARCH: {query}")
        try:
            answer = self.products_qa_chain.invoke({"input": query})
            result = answer["answer"]
            logger.info(f"🎓 Products search result: {result}")
            return f"🎓 Product/Course Search Results:\n{result}"
        except Exception as e:
            error_msg = f"Error searching products database: {str(e)}"
            logger.error(error_msg)
            return error_msg


# Global instance - will be created only once
_vector_store_manager = None


def get_vector_store_manager() -> VectorStoreManager:
    """Get the global vector store manager instance"""
    global _vector_store_manager
    if _vector_store_manager is None:
        _vector_store_manager = VectorStoreManager()
    return _vector_store_manager
