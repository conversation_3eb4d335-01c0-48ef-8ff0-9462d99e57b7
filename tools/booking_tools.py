from datetime import datetime, timedelta
from langchain_core.tools import tool
from .date_tool import get_current_date

# Sample appointment slots
appointment_slots = []
base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
for i in range(14):  # Next 14 days
    date = base_date + timedelta(days=i)
    for hour in [9, 11, 14, 16]:  # Available slots: 9AM, 11AM, 2PM, 4PM
        appointment_slots.append({
            "date": date.replace(hour=hour).strftime("%Y-%m-%d"),
            "time": date.replace(hour=hour).strftime("%H:%M"),
            "available": True
        })

# Store booked appointments
booked_appointments = []


@tool
def get_available_slots(service_type: str = "any", days_ahead: int = 14) -> str:
    """
    Get available appointment slots. This tool automatically gets the current date first.

    Args:
        service_type: Type of service (optional)
        days_ahead: Number of days to look ahead

    Returns:
        List of available appointment slots with current date information
    """
    # First get current date information
    current_date_info = get_current_date.invoke({})

    available_slots = []
    base_date = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)

    for i in range(1, days_ahead + 1):  # Start from tomorrow
        date = base_date + timedelta(days=i)
        # Skip weekends
        if date.weekday() < 5:  # Monday=0, Friday=4
            for hour in [9, 11, 14, 16]:  # 9AM, 11AM, 2PM, 4PM
                slot_datetime = date.replace(hour=hour)
                slot_info = {
                    "date": slot_datetime.strftime("%Y-%m-%d"),
                    "time": slot_datetime.strftime("%H:%M"),
                    "day": slot_datetime.strftime("%A"),
                    "formatted": slot_datetime.strftime("%B %d, %Y at %I:%M %p")
                }
                available_slots.append(slot_info)

    if not available_slots:
        return f"{current_date_info}\n\nNo available slots found."

    # Show first 10 slots
    result = f"{current_date_info}\n\n📅 Available Appointment Slots:\n\n"
    for i, slot in enumerate(available_slots[:10]):
        result += f"{i+1}. {slot['formatted']} ({slot['date']} {slot['time']})\n"

    if len(available_slots) > 10:
        result += f"\n... and {len(available_slots) - 10} more slots available."

    return result


@tool
def book_appointment(name: str, email: str, phone: str, date: str, time: str, service_type: str = "General Consultation") -> str:
    """
    Book an appointment with customer details.
    
    Args:
        name: Customer's full name
        email: Customer's email address
        phone: Customer's phone number
        date: Preferred date in YYYY-MM-DD format
        time: Preferred time in HH:MM format
        service_type: Type of service needed
    
    Returns:
        Confirmation message with appointment details
    """
    # Validate inputs
    if not all([name, email, phone, date, time]):
        return "Error: All fields (name, email, phone, date, time) are required to book an appointment."
    
    # Check if the slot is available
    requested_slot = None
    for slot in appointment_slots:
        if slot["date"] == date and slot["time"] == time:
            requested_slot = slot
            break
    
    if not requested_slot:
        return f"Error: No appointment slot available for {date} at {time}. Please check available slots."
    
    if not requested_slot["available"]:
        return f"Error: The slot on {date} at {time} is already booked. Please choose another time."
    
    # Book the appointment
    appointment_id = f"APT-{len(booked_appointments) + 1:04d}"
    appointment = {
        "id": appointment_id,
        "name": name,
        "email": email,
        "phone": phone,
        "date": date,
        "time": time,
        "service_type": service_type,
        "status": "confirmed"
    }
    
    booked_appointments.append(appointment)
    requested_slot["available"] = False
    
    return f"""
Appointment Successfully Booked!

Appointment ID: {appointment_id}
Name: {name}
Email: {email}
Phone: {phone}
Date: {date}
Time: {time}
Service: {service_type}
Status: Confirmed

Please save your appointment ID for future reference.
A confirmation email will be sent to {email}.
"""


@tool
def initiate_booking(service_type: str = "General Consultation") -> str:
    """
    Initiate the booking process. This tool automatically gets current date and shows available slots.
    Use this when someone says "book me in", "I want to book", "schedule appointment", etc.

    Args:
        service_type: Type of service needed (optional)

    Returns:
        Current date information and available appointment slots
    """
    # Automatically get current date and available slots
    return get_available_slots.invoke({"service_type": service_type})
