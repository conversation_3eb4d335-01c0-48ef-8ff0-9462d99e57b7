"""
Search Sub-Agent with specialized search tools for information and products.
This module provides two distinct search capabilities:
1. Information Search - for general info, troubleshooting, apps, services
2. Product Search - for courses, educational products, booking-related queries
"""

import os
from dotenv import load_dotenv
from langchain_core.tools import tool
from qdrant_client import QdrantClient
from langchain_openai import OpenAIEmbeddings
from langchain_qdrant import QdrantVectorStore, RetrievalMode
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.prompts import ChatPromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI

# Load environment variables
load_dotenv()

# Setup embeddings for Qdrant
embeddings = OpenAIEmbeddings(
    model="text-embedding-3-large",
    api_key=os.getenv("OPENAI_API_KEY"),
    dimensions=1536
)

# Initialize Qdrant client
qdrant_client = QdrantClient(
    host="*************",
    port=6333,
)

# Setup Information Vector Store (existing collection for general info)
info_vector_store = QdrantVectorStore(
    client=qdrant_client,
    collection_name="langsmit_test",
    embedding=embeddings,
    retrieval_mode=RetrievalMode.DENSE,
    content_payload_key="page_content",
    metadata_payload_key="metadata"
)

# Setup Products Vector Store (products collection for courses/products)
products_vector_store = QdrantVectorStore(
    client=qdrant_client,
    collection_name="langchain_products",
    embedding=embeddings,
    retrieval_mode=RetrievalMode.DENSE,
    content_payload_key="page_content",
    metadata_payload_key="metadata"
)

# Create retrievers
info_retriever = info_vector_store.as_retriever(search_type="mmr", search_kwargs={"k": 5})
products_retriever = products_vector_store.as_retriever(search_type="mmr", search_kwargs={"k": 5})

# Setup LLM for chains
llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

# Create prompts for different search types
info_system_prompt = (
    "You are an assistant for question-answering tasks about general information, "
    "troubleshooting, apps, and services. Use the following pieces of retrieved context "
    "to answer the question. If you don't know the answer, say that you don't know. "
    "Use three sentences maximum and keep the answer concise."
    "\n\n"
    "{context}"
)

products_system_prompt = (
    "You are an assistant for product and course information. Use the following pieces "
    "of retrieved context to answer questions about available courses, programs, "
    "and educational products. Include course codes, names, and descriptions when relevant. "
    "If you don't know the answer, say that you don't know. "
    "Use three sentences maximum and keep the answer concise."
    "\n\n"
    "{context}"
)

info_prompt = ChatPromptTemplate.from_messages([
    ("system", info_system_prompt),
    ("human", "{input}"),
])

products_prompt = ChatPromptTemplate.from_messages([
    ("system", products_system_prompt),
    ("human", "{input}"),
])

# Create document chains
info_question_answer_chain = create_stuff_documents_chain(llm, info_prompt)
products_question_answer_chain = create_stuff_documents_chain(llm, products_prompt)

# Create retrieval chains
info_qa_chain = create_retrieval_chain(info_retriever, info_question_answer_chain)
products_qa_chain = create_retrieval_chain(products_retriever, products_question_answer_chain)


@tool
def search_information(query: str) -> str:
    """
    Search for general information, troubleshooting, apps, and services.
    Use this for questions about how to use apps, troubleshooting issues, 
    general information, and non-product related queries.

    Args:
        query: The search term to look for

    Returns:
        A formatted string with search results
    """
    try:
        answer = info_qa_chain.invoke({"input": query})
        result = answer["answer"]
        return f"📋 Information Search Results:\n{result}"
    except Exception as e:
        return f"Error searching information database: {str(e)}"


@tool
def search_products(query: str) -> str:
    """
    Search for products, courses, and educational programs.
    Use this for questions about available courses, course details,
    booking courses, course codes, and product information.

    Args:
        query: The search term to look for (course name, subject, etc.)

    Returns:
        A formatted string with product/course search results
    """
    try:
        answer = products_qa_chain.invoke({"input": query})
        result = answer["answer"]
        return f"🎓 Product/Course Search Results:\n{result}"
    except Exception as e:
        return f"Error searching products database: {str(e)}"


# Export the search tools
__all__ = ['search_information', 'search_products']
